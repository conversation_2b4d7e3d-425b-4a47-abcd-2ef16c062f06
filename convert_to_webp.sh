#!/bin/bash

# Script to convert PNG images to WebP format
# Especially targeting the lovable-uploads directory

# Set the quality for WebP conversion (0-100, higher is better quality but larger file)
QUALITY=80

# Quality for blur placeholder images (very low for small file size)
BLUR_QUALITY=10

# Size for blur placeholder images (small dimensions for performance)
BLUR_SIZE=20

# Flag to force conversion even if WebP already exists
FORCE=false

# Flag to generate blur placeholders
GENERATE_BLUR=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -f|--force)
      FORCE=true
      shift
      ;;
    -q|--quality)
      QUALITY="$2"
      shift 2
      ;;
    -b|--blur)
      GENERATE_BLUR=true
      shift
      ;;
    --no-blur)
      GENERATE_BLUR=false
      shift
      ;;
    -bs|--blur-size)
      BLUR_SIZE="$2"
      shift 2
      ;;
    -bq|--blur-quality)
      BLUR_QUALITY="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [-f|--force] [-q|--quality VALUE] [-b|--blur] [--no-blur] [-bs|--blur-size SIZE] [-bq|--blur-quality VALUE]"
      exit 1
      ;;
  esac
done

# Function to create a blurred placeholder image
create_blur_placeholder() {
  local input_file="$1"
  local dir="$(dirname "$input_file")"
  local base_name="$(basename "$input_file" .png)"
  local blur_file="${dir}/${base_name}-blur.webp"
  
  # Skip if blur placeholder already exists and is not empty (unless FORCE is true)
  if [ "$FORCE" = false ] && [ -f "$blur_file" ] && [ -s "$blur_file" ]; then
    echo "⏭️  Skipping blur: $blur_file already exists"
    return 0
  fi
  
  echo "Creating blur placeholder: $blur_file"
  
  # Create a temporary file for the resized image
  temp_file=$(mktemp).png
  
  # Resize to small dimensions for the blur placeholder
  sips --resampleHeightWidth $BLUR_SIZE $BLUR_SIZE "$input_file" --out "$temp_file" > /dev/null 2>&1
  
  if [ $? -eq 0 ]; then
    # Apply a slight blur using sips (macOS built-in)
    sips --setProperty format jpeg "$temp_file" --out "${temp_file%.png}.jpg" > /dev/null 2>&1
    
    # Convert to WebP with very low quality for small file size
    cwebp -q $BLUR_QUALITY "${temp_file%.png}.jpg" -o "$blur_file"
    
    # Clean up temp files
    rm -f "$temp_file" "${temp_file%.png}.jpg"
    
    if [ -s "$blur_file" ]; then
      blur_size=$(du -h "$blur_file" | cut -f1)
      echo "✅ Created blur placeholder: $blur_file ($blur_size)"
    else
      echo "❌ Failed to create blur placeholder"
      rm -f "$blur_file"  # Remove empty file
    fi
  else
    echo "❌ Failed to resize for blur placeholder"
  fi
}

# Function to convert a single file
convert_file() {
  local input_file="$1"
  local output_file="${input_file%.png}.webp"
  
  # Skip if WebP version already exists and is not empty (unless FORCE is true)
  if [ "$FORCE" = false ] && [ -f "$output_file" ] && [ -s "$output_file" ]; then
    echo "⏭️  Skipping: $output_file already exists"
    return 0
  fi
  
  # Remove empty WebP files if they exist
  if [ -f "$output_file" ] && [ ! -s "$output_file" ]; then
    rm -f "$output_file"
    echo "🗑️  Removed empty file: $output_file"
  fi
  
  # Check image dimensions before conversion (using sips which is built into macOS)
  dimensions=$(sips -g pixelHeight -g pixelWidth "$input_file" 2>/dev/null)
  if [ $? -eq 0 ]; then
    width=$(echo "$dimensions" | grep pixelWidth | awk '{print $2}')
    height=$(echo "$dimensions" | grep pixelHeight | awk '{print $2}')
    
    # WebP has a max dimension of 16383 pixels
    if [ "$width" -gt 16383 ] || [ "$height" -gt 16383 ]; then
      echo "⚠️  Warning: $input_file dimensions (${width}x${height}) exceed WebP limits (max 16383px)"
      echo "   Resizing image before conversion..."
      
      # Create a temporary file for the resized image
      temp_file=$(mktemp).png
      
      # Calculate new dimensions while maintaining aspect ratio
      if [ "$width" -gt "$height" ]; then
        # Width is the limiting factor
        new_width=16000
        new_height=$(echo "scale=0; $height * 16000 / $width" | bc)
      else
        # Height is the limiting factor
        new_height=16000
        new_width=$(echo "scale=0; $width * 16000 / $height" | bc)
      fi
      
      # Resize the image using sips
      sips --resampleHeightWidth $new_height $new_width "$input_file" --out "$temp_file" > /dev/null 2>&1
      
      if [ $? -eq 0 ]; then
        echo "   Resized to ${new_width}x${new_height}"
        input_file="$temp_file"
        # We'll clean up the temp file after conversion
      else
        echo "   Failed to resize image, skipping conversion"
        return 1
      fi
    fi
  else
    echo "⚠️  Warning: Could not determine dimensions for $input_file"
  fi
  
  # Track if we're using a temp file for cleanup later
  is_temp_file=false
  if [[ "$input_file" == *"/tmp/"* ]]; then
    is_temp_file=true
  fi

  echo "Converting: $input_file to $output_file"
  cwebp -q $QUALITY "$input_file" -o "$output_file"
  
  # Clean up temp file if we created one
  if [ "$is_temp_file" = true ]; then
    rm -f "$input_file"
    echo "   Cleaned up temporary resized file"
  fi
  
  if [ $? -eq 0 ]; then
    # Verify the output file has content
    if [ -s "$output_file" ]; then
      # Get file sizes for comparison
      if [ "$is_temp_file" = true ]; then
        # For resized images, compare with the original
        original_file="${output_file%.webp}.png"
        original_size=$(du -h "$original_file" | cut -f1)
      else
        original_size=$(du -h "$input_file" | cut -f1)
      fi
      new_size=$(du -h "$output_file" | cut -f1)
      echo "✅ Success: ${output_file%.webp}.png ($original_size) → $output_file ($new_size)"
    else
      echo "❌ Conversion failed: Output file is empty"
      rm -f "$output_file"  # Remove empty file
    fi
  else
    echo "❌ Failed to convert: $input_file"
    rm -f "$output_file"  # Remove any partial output
  fi
}

# Function to count blur placeholders
count_blur_placeholders() {
  local count=$(find public -name "*-blur.webp" | wc -l)
  echo "✅ Created $count blur placeholder images"
}

# Main function
main() {
  echo "Starting conversion of PNG images to WebP format..."
  
  # Find all PNG files in the public directory
  find public -type f -name "*.png" | while read -r file; do
    # Convert to WebP
    convert_file "$file"
    
    # Create blur placeholder if enabled
    if [ "$GENERATE_BLUR" = true ]; then
      create_blur_placeholder "$file"
    fi
  done
  
  # Count and report the number of blur placeholders if they were generated
  if [ "$GENERATE_BLUR" = true ]; then
    count_blur_placeholders
  fi
  
  echo "Conversion complete!"
}

# Run the main function
main
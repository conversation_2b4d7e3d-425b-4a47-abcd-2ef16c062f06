import { ExternalLink } from "lucide-react";

const About = () => {
  return (
    <section id="about" className="py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="grid lg:grid-cols-5 gap-12 items-center relative">
          {/* Content Section */}
          <div className="lg:col-span-3">
            {/* About chip aligned to top left */}
            <div className="mb-8">
              <div className="inline-flex items-center gap-2 bg-orange-500/20 backdrop-blur-md border border-orange-500/30 text-orange-600 px-4 py-2 rounded-full text-sm font-medium">
                <span className="text-orange-500 animate-pulse">✦</span>
                About
              </div>
            </div>
            
            <h2 className="text-3xl lg:text-4xl font-semibold mb-6 leading-tight text-gray-900">
              Hi, I'm <PERSON>, brand and web designer <span className="text-gray-500">focused on business growth.</span>
            </h2>
            
            <div className="space-y-4 text-gray-700 text-lg leading-relaxed">
              <p>
                I've redesigned brands and websites for over 250 companies, helping them attract more customers, 
                build trust, and stand out from the competition.
              </p>
              
              <p>
                For 25+ years, I've led design for companies big and small, from Fortune 500 brands to 
                fast-growing startups. But what I've learned is simple: the businesses that win are the 
                ones that look like winners.
              </p>
              
              <div className="pt-4">
                <p className="text-gray-900 font-medium mb-3">That's where I come in. I help companies like yours:</p>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-center gap-3">
                    <span className="w-1.5 h-1.5 bg-orange-500 rounded-full"></span>
                    Refresh outdated logos and websites
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="w-1.5 h-1.5 bg-orange-500 rounded-full"></span>
                    Create clean, modern designs that look high-end
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="w-1.5 h-1.5 bg-orange-500 rounded-full"></span>
                    Turn cluttered pages into clear, professional experiences
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="w-1.5 h-1.5 bg-orange-500 rounded-full"></span>
                    Build confidence with your audience
                  </li>
                </ul>
              </div>
              
              <p className="pt-4 text-gray-900 font-medium">
                Let's make something great together.
              </p>
            </div>
          </div>

          {/* Profile Section */}
          <div className="lg:col-span-2 text-center">
            <div className="relative">
              <div className="w-64 h-64 mx-auto rounded-2xl overflow-hidden border-4 border-gray-200 shadow-lg">
                <img 
                  src="/lovable-uploads/0abbd9fe-2b9c-4770-875b-881c520b50c9.webp" 
                  alt="Mario Garcia" 
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Social Links */}
              <div className="flex justify-center gap-4 mt-6">
                <a 
                  href="https://www.linkedin.com/in/mariogarciajr/" 
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900 px-4 py-2 rounded-lg font-medium transition-all group"
                >
                  LinkedIn 
                  <ExternalLink className="w-4 h-4 group-hover:translate-x-0.5 transition-transform" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
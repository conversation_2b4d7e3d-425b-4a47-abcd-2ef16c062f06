import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Calendar, Users } from "lucide-react";

const cohortFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  companyName: z.string().min(2, "Company name must be at least 2 characters"),
  companyUrl: z.string().optional(),
  aboutYourself: z.string().min(20, "Please provide more details about yourself"),
  whatAreYouBuilding: z.string().min(20, "Please describe what you're building"),
  fundingStage: z.enum(["pre-seed", "seed", "series-a", "bootstrapped", "other"]),
  cohortGoals: z.string().min(20, "Please describe your goals for the cohort"),
  raiseTimeline: z.enum(["immediately", "soon", "later", "not-sure"]),
  additionalInfo: z.string().optional(),
});

type CohortFormData = z.infer<typeof cohortFormSchema>;

const CohortApplicationForm = () => {
  const form = useForm<CohortFormData>({
    resolver: zodResolver(cohortFormSchema),
    defaultValues: {
      name: "",
      email: "",
      companyName: "",
      companyUrl: "",
      aboutYourself: "",
      whatAreYouBuilding: "",
      fundingStage: "pre-seed",
      cohortGoals: "",
      raiseTimeline: "immediately",
      additionalInfo: "",
    },
  });

  const onSubmit = (data: CohortFormData) => {
    console.log("Form submitted:", data);
    // Handle form submission here
  };

  return (
    <div className="max-w-4xl mx-auto pt-32 pb-16 px-6">
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
          Apply to Join the Founder Cohort
        </h1>
        <div className="flex items-center justify-center gap-2 text-muted-foreground mb-4">
          <Calendar className="w-4 h-4" />
          <span>Up to 10 founders. $7,500 per company. Next cohort begins June 1, 2025.</span>
        </div>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          This application helps us understand where you are and whether this sprint is the right fit. 
          If accepted, you'll receive a kickoff invitation and payment details to secure your spot.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-12">
          {/* Your Details Section */}
          <div className="space-y-6">
            <h2 className="text-2xl font-semibold text-foreground">Your Details</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Your Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Full name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Company name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="companyUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company URL (optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="https://..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="aboutYourself"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tell us about yourself</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Share your background and why you're passionate about this idea..."
                      className="min-h-[120px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* About Your Startup Section */}
          <div className="space-y-6">
            <h2 className="text-2xl font-semibold text-foreground">About Your Startup</h2>
            
            <FormField
              control={form.control}
              name="whatAreYouBuilding"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>What are you building?</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Tell us about your product or service in a few sentences..."
                      className="min-h-[120px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="fundingStage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>What's your current funding stage?</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="grid grid-cols-1 md:grid-cols-3 gap-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="pre-seed" id="pre-seed" />
                        <label htmlFor="pre-seed" className="text-sm font-medium cursor-pointer">
                          Pre-seed
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="seed" id="seed" />
                        <label htmlFor="seed" className="text-sm font-medium cursor-pointer">
                          Seed
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="series-a" id="series-a" />
                        <label htmlFor="series-a" className="text-sm font-medium cursor-pointer">
                          Series A
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="bootstrapped" id="bootstrapped" />
                        <label htmlFor="bootstrapped" className="text-sm font-medium cursor-pointer">
                          Bootstrapped
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="other" id="other" />
                        <label htmlFor="other" className="text-sm font-medium cursor-pointer">
                          Other
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* About the Cohort Section */}
          <div className="space-y-6">
            <h2 className="text-2xl font-semibold text-foreground">About the Cohort</h2>
            
            <FormField
              control={form.control}
              name="cohortGoals"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>What are you hoping to get out of this cohort?</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Share your goals and what you're looking to achieve..."
                      className="min-h-[120px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="raiseTimeline"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>How soon are you looking to raise capital or grow?</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="grid grid-cols-1 md:grid-cols-2 gap-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="immediately" id="immediately" />
                        <label htmlFor="immediately" className="text-sm font-medium cursor-pointer">
                          Immediately (1-3 months)
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="soon" id="soon" />
                        <label htmlFor="soon" className="text-sm font-medium cursor-pointer">
                          Soon (3-6 months)
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="later" id="later" />
                        <label htmlFor="later" className="text-sm font-medium cursor-pointer">
                          Later (6+ months)
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="not-sure" id="not-sure" />
                        <label htmlFor="not-sure" className="text-sm font-medium cursor-pointer">
                          Not sure yet
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="additionalInfo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Anything else you'd like us to know? (optional)</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Any other information you'd like to share..."
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-center pt-8">
            <Button 
              type="submit" 
              size="lg"
              className="px-12 py-6 text-lg"
            >
              Submit Application
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CohortApplicationForm;
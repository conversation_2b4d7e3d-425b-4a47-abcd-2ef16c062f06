
import { Link } from "react-router-dom";

const projects = [
  {
    title: "Florida Funders | Rebrand and Website Design",
    slug: "floridafunders",
    image: "/lovable-uploads/2f26e8d5-cffc-4adb-b27e-7af5411c70c6.webp",
    tags: ["Web Design", "Brand Identity"],
    size: "large"
  },
  {
    title: "New Brand Identity for GHWIN", 
    slug: "ghwin-case-study",
    image: "/lovable-uploads/04f55e1e-4ba1-4f78-99f0-8be0d0bd4c98.webp",
    tags: ["Brand Identity"],
    size: "large"
  },
  {
    title: "Flare | AI Platform for Banks",
    slug: "flare-ai",
    image: "/lovable-uploads/0dbb6b31-8eba-4b4a-bc75-a00559542e2d.webp",
    tags: ["UI/UX", "Web Design", "Brand Identity"],
    size: "large"
  },
  {
    title: "Defy Medical | Landing page redesign",
    slug: "defy-medical",
    image: "/lovable-uploads/ca9b1e05-c04a-43e9-a477-5a235072ed68.webp",
    tags: ["Website Design"],
    size: "medium"
  },
  {
    title: "Logo Update and Website Redesign for CSD",
    slug: "csd-redesign",
    image: "/lovable-uploads/********-ff6a-4b68-a722-c65f2c70cce3.webp",
    tags: ["Brand Identity", "Web Design"],
    size: "medium"
  }
];

const FeaturedWork = () => {
  return (
    <section className="py-32 px-6 bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Header with Works badge */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-orange-500/20 backdrop-blur-md border border-orange-500/30 text-orange-600 px-4 py-2 rounded-full text-sm font-medium mb-8">
            <span className="text-orange-500 animate-pulse">✦</span>
            Work
          </div>
          <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-6 leading-tight">
            Recent Projects
          </h2>
        </div>
        
        {/* Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <Link 
              key={index} 
              to={`/case-study/${project.slug}`} 
              className={`group block ${
                project.size === 'large' ? 'md:col-span-1' : 'md:col-span-1'
              } ${index === 0 ? 'md:row-span-2' : ''}`}
            >
              <div className="relative overflow-hidden rounded-3xl bg-gray-900 h-full min-h-[400px] hover:transform hover:scale-[1.02] transition-all duration-500">
                {/* Project Image */}
                <div className="absolute inset-0 overflow-hidden rounded-3xl">
                  <img 
                    src={project.image} 
                    alt={project.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300"></div>
                </div>
                
                {/* Content Overlay */}
                <div className="absolute inset-0 p-8 flex flex-col justify-between">
                  {/* Tags */}
                  <div className="flex flex-wrap gap-2">
                    {project.tags.map((tag, tagIndex) => (
                      <span 
                        key={tagIndex}
                        className="px-3 py-1 bg-black/50 backdrop-blur-sm text-white text-sm rounded-full border border-white/20"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  
                  {/* Title */}
                  <div>
                    <h3 className="text-2xl md:text-3xl font-bold text-white leading-tight group-hover:transform group-hover:translate-y-[-4px] transition-transform duration-300">
                      {project.title}
                    </h3>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedWork;

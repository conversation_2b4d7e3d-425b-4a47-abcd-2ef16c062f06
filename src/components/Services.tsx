import { Lightbulb, Code, Palette } from "lucide-react";

const Services = () => {
  const services = [
    {
      icon: Palette,
      title: "UI/UX Design",
      description: "We Craft Seamless, Simple, Engaging, User-Friendly, We Design for Conversions"
    },
    {
      icon: Code,
      title: "Web Design", 
      description: "Emphasizes the functional aspect of web design. Highlights user experience and positive business outcomes."
    },
    {
      icon: Lightbulb,
      title: "Branding",
      description: "All-in-One Branding in one place. Our Branding that elevate your business."
    }
  ];

  return (
    <section id="services" className="py-24 px-6">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-500/20 border border-orange-500/30 text-orange-400 text-sm font-medium rounded-full mb-8">
            <span className="text-orange-500">✦</span>
            Services
          </div>
          
          <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-8 leading-tight">
            What We Offer
          </h2>
          
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            I work with clients on Branding, Web Design & Development, UI/UX Design, 
            <br />
            Web and Mobile App Development, AI Automation and Dashboard Design
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <div 
                key={index}
                className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-white/40 shadow-sm hover:shadow-md transition-all duration-300"
              >
                <div className="mb-6">
                  <IconComponent className="w-12 h-12 text-gray-600" strokeWidth={1.5} />
                </div>
                
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                  {service.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed">
                  {service.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Services;

const ProcessSection = () => {
  const processSteps = [
    {
      title: "Discovery & Research",
      items: [
        "Client interviews and brand questionnaire",
        "Competitor analysis of local and chain coffee shops",
        "Target audience research and personas",
        "Brand positioning workshop"
      ]
    },
    {
      title: "Brand Strategy",
      items: [
        "Brand personality: Warm, authentic, community-focused",
        "Key messaging: \"Your neighborhood's coffee crafters\"",
        "Visual direction: Handcrafted, approachable, quality-focused"
      ]
    },
    {
      title: "Visual Identity Development",
      items: [
        "Logo concepts and iterations",
        "Color palette selection (warm browns, cream, forest green)",
        "Typography choices (modern serif for warmth, clean sans-serif for clarity)",
        "Brand pattern and texture development"
      ]
    },
    {
      title: "Website Design",
      items: [
        "Information architecture and user flow",
        "Mobile-first responsive design",
        "E-commerce integration for online ordering",
        "Location and hours prominence"
      ]
    }
  ];

  return (
    <section className="py-20 border-t border-gray-100">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-6">
          Design Process
        </h2>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          A systematic approach to creating meaningful brand experiences
        </p>
      </div>

      <div className="space-y-12">
        {processSteps.map((step, index) => (
          <div key={index} className="bg-gray-50 rounded-2xl p-8 md:p-10">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="md:w-1/3">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    {index + 1}
                  </div>
                  <h3 className="text-2xl font-medium text-gray-900">
                    {step.title}
                  </h3>
                </div>
              </div>
              <div className="md:w-2/3">
                <ul className="space-y-3">
                  {step.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-700 leading-relaxed">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default ProcessSection;

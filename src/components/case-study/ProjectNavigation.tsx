
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

const ProjectNavigation = () => {
  return (
    <section className="py-16 bg-gray-50 border-t border-gray-100">
      <div className="max-w-6xl mx-auto px-6">
        <div className="flex flex-col md:flex-row items-center justify-between gap-8">
          {/* Previous Project */}
          <div className="flex items-center gap-4">
            <Button variant="outline" size="lg" className="flex items-center gap-2">
              <ArrowLeft className="w-5 h-5" />
              Previous Project
            </Button>
            <div className="text-left">
              <p className="text-sm text-gray-600">TechStart Solutions</p>
              <p className="font-medium text-gray-900">SaaS Platform Design</p>
            </div>
          </div>

          {/* View All Projects */}
          <Link to="/">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              View All Projects
            </Button>
          </Link>

          {/* Next Project */}
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm text-gray-600">Luna Wellness Studio</p>
              <p className="font-medium text-gray-900">Wellness Brand Identity</p>
            </div>
            <Button variant="outline" size="lg" className="flex items-center gap-2">
              Next Project
              <ArrowRight className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Related Projects */}
        <div className="mt-16 pt-16 border-t border-gray-200">
          <h3 className="text-2xl font-light text-gray-900 mb-8 text-center">
            Related Projects
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            {[1, 2, 3].map((item) => (
              <div key={item} className="group cursor-pointer">
                <div className="aspect-[4/3] bg-gray-200 rounded-lg mb-4 overflow-hidden">
                  <img 
                    src={`https://images.unsplash.com/photo-148838050${item}505-72967e2e2760?w=400&h=300&fit=crop`}
                    alt={`Related project ${item}`}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h4 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                  Related Project {item}
                </h4>
                <p className="text-gray-600 text-sm">Brand Identity</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectNavigation;


const VisualStory = () => {
  const images = [
    {
      src: "https://images.unsplash.com/photo-1493857671505-72967e2e2760?w=600&h=400&fit=crop",
      alt: "Before/after logo comparison",
      caption: "Before/After Logo Evolution"
    },
    {
      src: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop", 
      alt: "Logo concept sketches",
      caption: "Initial Concept Sketches"
    },
    {
      src: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop",
      alt: "Final logo applications",
      caption: "Logo Applications"
    },
    {
      src: "https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=600&h=400&fit=crop",
      alt: "Website desktop view",
      caption: "Website Desktop Design"
    },
    {
      src: "https://images.unsplash.com/photo-1493857671505-72967e2e2760?w=600&h=800&fit=crop",
      alt: "Website mobile view",
      caption: "Mobile-First Responsive Design"
    },
    {
      src: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop",
      alt: "Brand collateral",
      caption: "Brand Collateral & Packaging"
    }
  ];

  return (
    <section className="py-20 border-t border-gray-100">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-6">
          Visual Story
        </h2>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          From initial concepts to final implementation
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {images.map((image, index) => (
          <div key={index} className="group cursor-pointer">
            <div className="relative overflow-hidden rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
              <img 
                src={image.src}
                alt={image.alt}
                className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            <p className="text-center text-gray-600 mt-4 font-medium">
              {image.caption}
            </p>
          </div>
        ))}
      </div>

      {/* Large Feature Image */}
      <div className="mt-16">
        <div className="relative overflow-hidden rounded-2xl shadow-2xl">
          <img 
            src="https://images.unsplash.com/photo-1493857671505-72967e2e2760?w=1200&h=600&fit=crop"
            alt="Real-world implementation"
            className="w-full h-96 md:h-[500px] object-cover"
          />
        </div>
        <p className="text-center text-gray-600 mt-6 text-lg">
          Real-world Implementation & Brand Activation
        </p>
      </div>
    </section>
  );
};

export default VisualStory;

import React, { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  priority?: boolean;
  sizes?: string;
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
  disableWebP?: boolean;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className,
  priority = false,
  sizes,
  blurDataURL,
  onLoad,
  onError,
  disableWebP = false,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || typeof window === "undefined") {
      setIsInView(true);
      return;
    }

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observerRef.current?.disconnect();
        }
      },
      {
        rootMargin: "50px 0px", // Start loading 50px before the image enters viewport
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      observerRef.current?.disconnect();
    };
  }, [priority]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Generate srcset for responsive images
  const generateSrcSet = (baseSrc: string) => {
    if (baseSrc.includes("unsplash.com")) {
      return `
        ${baseSrc}&w=400 400w,
        ${baseSrc}&w=800 800w,
        ${baseSrc}&w=1200 1200w,
        ${baseSrc}&w=1600 1600w
      `.trim();
    }
    return undefined;
  };

  // Convert image path to WebP or PNG
  const getWebPPath = (imagePath: string): string => {
    // If already a WebP image, return as is
    if (imagePath.toLowerCase().endsWith(".webp")) {
      return imagePath;
    }

    // Replace extension with .webp or add .webp if no extension
    const basePath = imagePath.split("?")[0]; // Remove query params if any
    const webpPath = basePath.replace(/\.[^/.]+$/, "") + ".webp";

    // Add back query params if they existed
    return imagePath.includes("?")
      ? `${webpPath}${imagePath.substring(imagePath.indexOf("?"))}`
      : webpPath;
  };

  // Get the fallback PNG path
  const getPngPath = (imagePath: string): string => {
    // If already ends with .png, return as is
    if (imagePath.toLowerCase().endsWith(".png")) {
      return imagePath;
    }

    // If it's a WebP, convert to PNG
    if (imagePath.toLowerCase().endsWith(".webp")) {
      return imagePath.replace(/\.webp$/i, ".png");
    }

    // Otherwise assume it needs .png extension
    const basePath = imagePath.split("?")[0]; // Remove query params if any
    const pngPath = basePath.replace(/\.[^/.]+$/, "") + ".png";

    // Add back query params if they existed
    return imagePath.includes("?")
      ? `${pngPath}${imagePath.substring(imagePath.indexOf("?"))}`
      : pngPath;
  };

  // Default blur data URL (base64 encoded 1x1 pixel)
  const defaultBlurDataURL =
    "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyrmrZ3VtcoylWVXQ+FdwCz/wB8n/ZEXK8PdhBfrpnCakY+zAPTyujF6zWOJ8dQ==";

  // State to track the actual blur URL to use
  const [actualBlurURL, setActualBlurURL] = useState<string>(
    blurDataURL || defaultBlurDataURL
  );

  // Effect to load and check the blur image
  useEffect(() => {
    // If explicit blurDataURL is provided, use it
    if (blurDataURL) {
      setActualBlurURL(blurDataURL);
      return;
    }

    // If we have a path, try to construct the blur version path
    if (src) {
      const basePath = src.split("?")[0]; // Remove query params if any
      const blurPath = basePath.replace(/\.[^/.]+$/, "") + "-blur.webp";

      // Create an image to test if the blur version exists
      const img = new Image();
      img.onload = () => {
        setActualBlurURL(blurPath);
      };
      img.onerror = () => {
        setActualBlurURL(defaultBlurDataURL);
      };
      img.src = blurPath;
    }
  }, [src, blurDataURL]);

  return (
    <div ref={imgRef} className={cn("relative overflow-hidden", className)}>
      {/* Blur placeholder */}
      {!isLoaded && (
        <div
          className="absolute inset-0 bg-gray-200 animate-pulse"
          style={{
            backgroundImage: `url(${actualBlurURL})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            filter: "blur(20px)",
            transform: "scale(1.1)", // Slightly larger to hide blur edges
          }}
        />
      )}

      {/* Error fallback */}
      {hasError && (
        <div className="absolute inset-0 bg-gray-300 flex items-center justify-center">
          <span className="text-gray-500 text-sm">Failed to load image</span>
        </div>
      )}

      {/* Actual image with WebP and PNG fallback */}
      {(isInView || priority) && (
        <picture>
          {!disableWebP && (
            <source
              srcSet={generateSrcSet(getWebPPath(src)) || getWebPPath(src)}
              type="image/webp"
              sizes={
                sizes ||
                "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              }
            />
          )}
          <img
            src={getPngPath(src)}
            srcSet={generateSrcSet(getPngPath(src))}
            sizes={
              sizes ||
              "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            }
            alt={alt}
            loading={priority ? "eager" : "lazy"}
            decoding="async"
            onLoad={handleLoad}
            onError={handleError}
            className={cn(
              "transition-opacity duration-300",
              isLoaded ? "opacity-100" : "opacity-0",
              className
            )}
            style={{
              objectFit: "cover",
              width: "100%",
              height: "100%",
            }}
          />
        </picture>
      )}
    </div>
  );
};

export default OptimizedImage;

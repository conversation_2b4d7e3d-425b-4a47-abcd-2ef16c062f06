
@tailwind base;
@tailwind components;
@tailwind utilities;


/* Custom Scrollbar Styling */
/* Always show scrollbar to prevent layout shift */
html {
  overflow-y: scroll;
  scrollbar-gutter: stable;
}

html body[data-scroll-locked] {
  --removed-body-scroll-bar-size: 0 !important;
  margin-right: 0 !important;
} 

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Wolfpixel-inspired background variables */
    --grid-dot-color: 220 13% 94%;
    --pastel-lavender: 260 50% 96%;
    --pastel-peach: 20 100% 96%;
    --pastel-sky: 200 100% 96%;
    --pastel-mint: 160 100% 96%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-helvectica;
    /* @apply font-sans; */
    @apply bg-background text-foreground;
    background-image: 
      /* Fine dot grid pattern */
      radial-gradient(circle at center, hsl(var(--grid-dot-color)) 0.5px, transparent 0.5px),
      /* Soft pastel color splashes positioned strategically */
      radial-gradient(ellipse 800px 600px at 15% 20%, hsl(var(--pastel-lavender)) 0%, transparent 40%),
      radial-gradient(ellipse 700px 500px at 85% 15%, hsl(var(--pastel-peach)) 0%, transparent 35%),
      radial-gradient(ellipse 600px 800px at 90% 85%, hsl(var(--pastel-sky)) 0%, transparent 45%),
      radial-gradient(ellipse 500px 400px at 10% 90%, hsl(var(--pastel-mint)) 0%, transparent 30%),
      radial-gradient(ellipse 400px 600px at 25% 75%, hsl(var(--pastel-lavender)) 0%, transparent 25%);
    background-size: 
      20px 20px,
      100% 100%,
      100% 100%,
      100% 100%,
      100% 100%,
      100% 100%;
    background-attachment: fixed;
    background-repeat: repeat, no-repeat, no-repeat, no-repeat, no-repeat, no-repeat;
  }
}

/* Grid background utility */
@layer utilities {
  .wolfpixel-bg {
    background-image: 
      radial-gradient(circle at center, hsl(var(--grid-dot-color)) 0.5px, transparent 0.5px),
      radial-gradient(ellipse 800px 600px at 15% 20%, hsl(var(--pastel-lavender)) 0%, transparent 40%),
      radial-gradient(ellipse 700px 500px at 85% 15%, hsl(var(--pastel-peach)) 0%, transparent 35%),
      radial-gradient(ellipse 600px 800px at 90% 85%, hsl(var(--pastel-sky)) 0%, transparent 45%),
      radial-gradient(ellipse 500px 400px at 10% 90%, hsl(var(--pastel-mint)) 0%, transparent 30%);
    background-size: 
      20px 20px,
      100% 100%,
      100% 100%,
      100% 100%,
      100% 100%;
    background-attachment: fixed;
    background-repeat: repeat, no-repeat, no-repeat, no-repeat, no-repeat;
  }
  
  .grid-bg {
    background-image: 
      linear-gradient(to right, hsl(var(--grid-color)) 1px, transparent 1px),
      linear-gradient(to bottom, hsl(var(--grid-color)) 1px, transparent 1px);
    background-size: 40px 40px;
  }
  
  .grid-bg-with-accents {
    background-image: 
      linear-gradient(to right, hsl(var(--grid-color)) 1px, transparent 1px),
      linear-gradient(to bottom, hsl(var(--grid-color)) 1px, transparent 1px),
      radial-gradient(circle at 20% 20%, hsl(var(--grid-accent-1)) 0%, transparent 30%),
      radial-gradient(circle at 80% 80%, hsl(var(--grid-accent-2)) 0%, transparent 25%),
      radial-gradient(circle at 60% 40%, hsl(var(--grid-accent-3)) 0%, transparent 20%);
    background-size: 40px 40px, 40px 40px, 600px 600px, 800px 800px, 500px 500px;
  }

  /* Scrolling animation for customer logos */
  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-175px * 10 - 64px * 10));
    }
  }

  @keyframes testimonial-scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-438px * 6 - 24px * 6 - 48px * 6));
    }
  }

  .animate-scroll {
    animation: scroll 25s linear infinite;
  }

  .animate-testimonial-scroll {
    animation: testimonial-scroll 90s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-testimonial-scroll:hover {
    animation-play-state: paused;
  }
}


import { useParams } from "react-router-dom";
import { useEffect } from "react";
import Navigation from "@/components/Navigation";
import ProjectOverview from "@/components/case-study/ProjectOverview";
import ProcessSection from "@/components/case-study/ProcessSection";
import VisualStory from "@/components/case-study/VisualStory";
import KeyInsights from "@/components/case-study/KeyInsights";
import ClientTestimonial from "@/components/case-study/ClientTestimonial";
import ProjectNavigation from "@/components/case-study/ProjectNavigation";
import ContactFooter from "@/components/ContactFooter";
import OptimizedImage from "@/components/ui/optimized-image";
import { useImagePreload } from "@/hooks/use-image-preload";

const CaseStudy = () => {
  const { slug } = useParams();
  
  // Scroll to top when component mounts or slug changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [slug]);
  
  // Preload critical images for better performance
  useImagePreload([
    "/lovable-uploads/98836210-5414-4157-860a-44dee16bb815.webp",
    "/FLF_crisp.webp",
    "/lovable-uploads/228a4d0e-679a-4ffd-809c-198f52473b64.webp"
  ], { priority: true });
  
  // Sample project data - this would typically come from a CMS or API
  const getProjectData = (slug: string) => {
    if (slug === 'csd-redesign') {
      return {
        title: "Elevating First Impressions: Logo & Website Overhaul for Creative Sign Designs",
        subtitle: "Creative Sign Designs | Brand Identity & Website Design",
        tagline: "Transforming a local sign company's identity to reflect quality craftsmanship and reliability",
      };
    }
    
    // Default to Florida Funders
    return {
      title: "Modernizing the Brand and Website of One of Florida's Leading VC Firms",
      subtitle: "Local Coffee Shop | Brand Identity & Website Design",
      tagline: "Creating a warm, community-focused brand that stands out from corporate chains",
    };
  };

  const projectData = {
    ...getProjectData(slug || ''),
    heroImage: "/lovable-uploads/98836210-5414-4157-860a-44dee16bb815.webp",
    beforeAfter: {
      website: {
        before: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=700&h=500&fit=crop",
        after: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=700&h=500&fit=crop"
      },
      logo: {
        before: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=300&fit=crop",
        after: "https://images.unsplash.com/photo-1634887522418-b6b48adc5327?w=400&h=300&fit=crop"
      }
    },
    overview: {
      challenge: "Help a local coffee shop differentiate from corporate chains while appealing to coffee enthusiasts and casual customers",
      solution: "Developed warm, artisanal branding that emphasizes community connection and quality craftsmanship",
      timeline: "6 weeks",
      results: "40% increase in social media engagement, 25% boost in weekend foot traffic"
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section with Large Image */}
      <div className="pt-48 pb-0">
        <div className="max-w-7xl mx-auto px-6">
          {/* Project Title */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-7xl font-semibold text-gray-900 mb-8 leading-[0.9] tracking-tight">
              {projectData.title}
            </h1>
            <div className="flex justify-center gap-8 text-sm text-gray-500 uppercase tracking-wider">
              <span>Brand Identity</span>
              <span>•</span>
              <span>Web Design</span>
              <span>•</span>
              <span>2025</span>
            </div>
          </div>
        </div>
        
        {/* Full Width Hero Image */}
        <div className="w-full">
          <OptimizedImage 
            src={projectData.heroImage}
            alt={projectData.title}
            className="w-full h-[70vh]"
            priority={true}
            sizes="100vw"
          />
        </div>
      </div>

      {/* Project Overview */}
      <div className="py-24">
        <div className="max-w-4xl mx-auto px-6">
          <div className="grid md:grid-cols-2 gap-16">
            <div>
              <h2 className="text-3xl font-semibold text-gray-900 mb-8">Project Overview</h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-8">
                Florida Funders was investing in cutting-edge innovation, but their brand and website didn't reflect the bold, visionary energy of their team or portfolio. The challenge was to elevate their visual identity to match their status as one of the top VC firms in the region.
              </p>
              <p className="text-gray-600 leading-relaxed mb-8">
                After presenting multiple logo directions, we landed on a modern, distinctive mark that felt uniquely "Florida Funders." I then delivered a complete website redesign that brought the brand to life—bold, forward-thinking, and built to resonate with both founders and investors.
              </p>
              <p className="text-gray-600 leading-relaxed">
                This project helped position Florida Funders as not just a capital partner, but a recognizable and memorable brand at the forefront of venture in the Southeast.
              </p>
            </div>
            <div className="space-y-8">
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Client</h3>
                <p className="text-gray-600">Florida Funders</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Timeline</h3>
                <p className="text-gray-600">4 months</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Services</h3>
                <p className="text-gray-600">Brand Identity, Website Design</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Before & After Section */}
      <div className="py-24">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-3 gap-8 items-start">
            {/* Before - Left side, smaller */}
            <div className="col-span-1">
              <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Before</div>
              <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200">
                <OptimizedImage 
                  src="/lovable-uploads/228a4d0e-679a-4ffd-809c-198f52473b64.webp"
                  alt="Website before redesign"
                  className="w-full h-auto"
                  priority={true}
                  sizes="(max-width: 768px) 100vw, 33vw"
                />
              </div>
            </div>
            
            {/* After - Right side, larger */}
            <div className="col-span-2">
              <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">After</div>
              <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
                <OptimizedImage 
                  src="/FLF_crisp.webp"
                  alt="Website after redesign"
                  className="w-full h-auto"
                  priority={true}
                  sizes="(max-width: 768px) 100vw, 66vw"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Final Brand Image */}
      <div className="py-12">
        <div className="max-w-7xl mx-auto px-6">
          <OptimizedImage 
            src="/lovable-uploads/9ce2f401-2b7e-4519-96df-061e8b8e785e.webp"
            alt="Florida Funders final brand design"
            className="w-full h-[32rem] rounded-2xl object-contain image-render-crisp"
            sizes="100vw"
          />
          <div className="mt-8 max-w-4xl mx-auto">
            <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">LOGO</div>
            <p className="text-gray-600 leading-relaxed mb-4">
              We explored a wide range of logo directions for Florida Funders—from modern sans-serifs to stylized letterforms and experimental lockups—each aimed at capturing a balance of innovation, credibility, and regional identity.
            </p>
            <p className="text-gray-600 leading-relaxed">
              The final mark is bold and geometric, using an abstract "FLF" monogram with a pixel-to-gradient transition that visually signals innovation and digital transformation. Paired with clean, modern typography, the logo feels future-focused and distinctive—perfectly aligned with Florida Funders&apos; mission to back the next generation of tech founders.
            </p>
          </div>
        </div>
      </div>

      {/* Website Pages Section */}
      <div className="py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
              <OptimizedImage 
                src="/lovable-uploads/5648929f-fba3-4ffc-a377-b1c2fb0d9659.webp"
                alt="Florida Funders About page"
                className="w-full h-auto"
                sizes="(max-width: 768px) 100vw, 33vw"
              />
            </div>
            <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
              <OptimizedImage 
                src="/lovable-uploads/a581d9c6-4b86-4965-82f8-3e60b239e2f4.webp"
                alt="Florida Funders Investment page"
                className="w-full h-auto"
                sizes="(max-width: 768px) 100vw, 33vw"
              />
            </div>
            <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
              <OptimizedImage 
                src="/lovable-uploads/37045f37-faa0-4912-8885-dc4322b98547.webp"
                alt="Florida Funders Resources page"
                className="w-full h-auto"
                sizes="(max-width: 768px) 100vw, 33vw"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Website Design Description */}
      <div className="py-16">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Designing the New Florida Funders Website</div>
          <div className="space-y-6 text-gray-600 leading-relaxed">
            <p>
              We started with a clear challenge: Florida Funders had a strong reputation and bold vision, but their brand and website didn't reflect it. The site felt outdated, generic, and disconnected from the innovative founders and forward-thinking investors they wanted to attract.
            </p>
            <p>
              To fix that, we began by modernizing their visual identity—presenting several diverse logo directions that ranged from refined and conservative to bold and tech-forward. After a few collaborative rounds, they selected a confident, geometric mark that nods to both Florida and funding—unique, memorable, and unmistakably modern.
            </p>
            <p>
              From there, the website came to life. I designed a new experience that was clean, high-contrast, and energized by the new brand gradients. We simplified the structure and made the pathways for founders and investors crystal clear, while still leaving room for storytelling. The result is a digital presence that now feels as sophisticated and forward-leaning as the firm itself.
            </p>
          </div>
        </div>
      </div>

      <ContactFooter />
    </div>
  );
};

export default CaseStudy;

import Navigation from "@/components/Navigation";
import CohortApplicationForm from "@/components/CohortApplicationForm";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

const CohortApplication = () => {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <main>
        <CohortApplicationForm />
      </main>
      
      {/* Custom Footer */}
      <footer className="bg-white py-24">
        <div className="max-w-6xl mx-auto px-6">
          {/* CTA Section */}
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-8 leading-tight">
              Ready to build a
              <br />
              fundable brand?
            </h2>
            
            <Button 
              className="bg-[#474787] hover:bg-[#3d3f73] text-white px-8 py-4 text-lg rounded-full font-semibold shadow-lg shadow-[#474787]/25 transition-all duration-200 hover:shadow-[#474787]/40 hover:scale-105 flex items-center gap-2 mx-auto mb-16 w-fit"
              asChild
            >
              <a href="/cohort-application">
                APPLY NOW
                <ArrowRight className="w-5 h-5" />
              </a>
            </Button>
          </div>

          {/* Navigation Links */}
          <div className="mb-16">
            <div className="flex flex-wrap justify-center gap-8 text-gray-600">
              <a href="#home" className="hover:text-gray-900 transition-colors">Home</a>
              <a href="#about" className="hover:text-gray-900 transition-colors">About</a>
              <a href="#works" className="hover:text-gray-900 transition-colors">Works</a>
              <a href="#contact" className="hover:text-gray-900 transition-colors">Contact</a>
            </div>
          </div>
          
          <div className="pt-8 border-t border-gray-200">
            <p className="text-gray-500 text-center">
              Copyright © 2025 Garcia Interactive. All Rights Reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default CohortApplication;
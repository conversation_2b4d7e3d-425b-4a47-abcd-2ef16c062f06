import Navigation from "@/components/Navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowRight, CheckCircle, Phone } from "lucide-react";
import { useState } from "react";
import {
  createContactEmailHtml,
  createContactEmailText,
} from "@/lib/emailTemplates";

const Contact = () => {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    service: "",
    budget: "",
    projectDetails: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError("");

    try {
      // Use the imported email template functions
      const htmlBody = createContactEmailHtml(formData);
      const textBody = createContactEmailText(formData);

      // Define the worker API URL - use environment variable or hardcoded value
      const workerUrl = "https://mario-portfolio-api.margar70.workers.dev";

      // Send email via the worker API
      const response = await fetch(`${workerUrl}/api/send-email`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        mode: "cors", // Explicitly set CORS mode
        credentials: "omit", // Explicitly omit credentials to avoid CORS issues
        body: JSON.stringify({
          to: "<EMAIL>",
          bcc: "<EMAIL>",
          subject: `New Project Inquiry from ${formData.fullName}`,
          htmlBody,
          textBody,
          replyTo: formData.email,
        }),
      });

      console.log("Response status:", response.status);

      if (!response.ok) {
        let errorMessage = "Failed to send email";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          console.error("Could not parse error response:", e);
        }
        throw new Error(errorMessage);
      }

      setSubmitSuccess(true);
      // Reset form
      setFormData({
        fullName: "",
        email: "",
        phone: "",
        service: "",
        budget: "",
        projectDetails: "",
      });
    } catch (error) {
      console.error("Error sending email:", error);
      setSubmitError(
        "Failed to send your inquiry. Please try again or contact us directly."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <section className="pt-32 pb-20 px-6">
        <div className="max-w-4xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-100 text-orange-600 rounded-full mb-8">
              <span>✦</span>
              Contact
            </div>

            <h1 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-6 leading-tight">
              Tell us about your
              <br />
              amazing project
            </h1>

            <div className="flex items-center justify-center gap-2 mb-8">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <p className="text-lg text-gray-600">
                We will respond to you within 24 hours.
              </p>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Full Name */}
              <div>
                <Label htmlFor="fullName" className="text-gray-700 font-medium">
                  Full name*
                </Label>
                <Input
                  id="fullName"
                  type="text"
                  placeholder="John Doe"
                  value={formData.fullName}
                  onChange={(e) =>
                    handleInputChange("fullName", e.target.value)
                  }
                  className="mt-2 bg-gray-50 border-gray-200 focus:border-orange-500 focus:ring-orange-500"
                  required
                />
              </div>

              {/* Email and WhatsApp */}
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="email" className="text-gray-700 font-medium">
                    Email*
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="mt-2 bg-gray-50 border-gray-200 focus:border-orange-500 focus:ring-orange-500"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="phone" className="text-gray-700 font-medium">
                    Phone Number (Optional)
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="****** 222 333"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    className="mt-2 bg-gray-50 border-gray-200 focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>
              </div>

              {/* Service and Budget */}
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <Label className="text-gray-700 font-medium">
                    Service required*
                  </Label>
                  <Select
                    onValueChange={(value) =>
                      handleInputChange("service", value)
                    }
                  >
                    <SelectTrigger className="mt-2 bg-gray-50 border-gray-200 focus:border-orange-500 focus:ring-orange-500">
                      <SelectValue placeholder="Select Your Service" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="web-design">Web Design</SelectItem>
                      <SelectItem value="web-development">
                        Web Development
                      </SelectItem>
                      <SelectItem value="branding">Branding</SelectItem>
                      <SelectItem value="ui-ux">UI/UX Design</SelectItem>
                      <SelectItem value="ecommerce">E-commerce</SelectItem>
                      <SelectItem value="consultation">Consultation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-gray-700 font-medium">
                    Project budget (Optional)
                  </Label>
                  <Select
                    onValueChange={(value) =>
                      handleInputChange("budget", value)
                    }
                  >
                    <SelectTrigger className="mt-2 bg-gray-50 border-gray-200 focus:border-orange-500 focus:ring-orange-500">
                      <SelectValue placeholder="Select Your Range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="under-5k">Under $5,000</SelectItem>
                      <SelectItem value="5k-10k">$5,000 - $10,000</SelectItem>
                      <SelectItem value="10k-25k">$10,000 - $25,000</SelectItem>
                      <SelectItem value="25k-50k">$25,000 - $50,000</SelectItem>
                      <SelectItem value="50k-100k">
                        $50,000 - $100,000
                      </SelectItem>
                      <SelectItem value="100k+">$100,000+</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Project Details */}
              <div>
                <Label
                  htmlFor="projectDetails"
                  className="text-gray-700 font-medium"
                >
                  Project Details*
                </Label>
                <Textarea
                  id="projectDetails"
                  placeholder="I want to redesign my website"
                  value={formData.projectDetails}
                  onChange={(e) =>
                    handleInputChange("projectDetails", e.target.value)
                  }
                  className="mt-2 bg-gray-50 border-gray-200 focus:border-orange-500 focus:ring-orange-500 min-h-[120px]"
                  required
                />
              </div>

              {/* Submit Button */}
              <div className="pt-4">
                <Button
                  type="submit"
                  size="lg"
                  className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-6 text-lg rounded-full transition-all duration-300 hover:scale-105 flex items-center gap-2 mx-auto"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Sending..." : "Send inquiry"}
                  {!isSubmitting && <ArrowRight className="w-5 h-5" />}
                </Button>
              </div>

              {/* Success Message */}
              {submitSuccess && (
                <div className="mt-6 p-4 bg-green-50 border border-green-100 rounded-lg text-center">
                  <CheckCircle className="w-6 h-6 text-green-500 mx-auto mb-2" />
                  <p className="text-green-800 font-medium">
                    Thank you! Your inquiry has been sent successfully.
                  </p>
                  <p className="text-green-600 text-sm mt-1">
                    We'll get back to you within 24 hours.
                  </p>
                </div>
              )}

              {/* Error Message */}
              {submitError && (
                <div className="mt-6 p-4 bg-red-50 border border-red-100 rounded-lg text-center">
                  <p className="text-red-800 font-medium">{submitError}</p>
                </div>
              )}
            </form>

            {/* Alternative Contact */}
            <div className="text-center mt-8 pt-8 border-t border-gray-100">
              <p className="text-gray-600 mb-4 italic">
                Not interested to submit the form?
              </p>
              <a
                href="https://calendly.com/mariogarciajr/meeting"
                className="inline-flex items-center gap-2 text-gray-900 hover:text-orange-500 transition-colors font-medium underline"
              >
                <Phone className="w-4 h-4" />
                Book A Call Directly
                <ArrowRight className="w-4 h-4" />
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;

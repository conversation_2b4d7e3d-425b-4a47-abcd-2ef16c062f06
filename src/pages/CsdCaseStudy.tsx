import { useEffect } from "react";
import Navigation from "@/components/Navigation";
import ContactFooter from "@/components/ContactFooter";
import OptimizedImage from "@/components/ui/optimized-image";
import { useImagePreload } from "@/hooks/use-image-preload";

const CsdCaseStudy = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  
  // Preload critical images for better performance
  useImagePreload([
    "/lovable-uploads/d42bdb0b-6334-4e83-8772-bbe263bdccbc.webp",
  ], { priority: true });

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section with Large Image */}
      <div className="pt-48 pb-0">
        <div className="max-w-7xl mx-auto px-6">
          {/* Project Title */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-7xl font-semibold text-gray-900 mb-8 leading-[0.9] tracking-tight">
              Elevating First Impressions: Logo & Website Overhaul for Creative Sign Designs
            </h1>
            <div className="flex justify-center gap-8 text-sm text-gray-500 uppercase tracking-wider">
              <span>Brand Identity</span>
              <span>•</span>
              <span>Web Design</span>
              <span>•</span>
              <span>2024</span>
            </div>
          </div>
        </div>
        
        {/* Full Width Hero Image */}
        <div className="w-full">
          <OptimizedImage 
            src="/lovable-uploads/d42bdb0b-6334-4e83-8772-bbe263bdccbc.webp"
            alt="Creative Sign Designs case study hero"
            className="w-full h-[70vh]"
            priority={true}
            sizes="100vw"
          />
        </div>
      </div>

      {/* Project Overview */}
      <div className="py-24">
        <div className="max-w-4xl mx-auto px-6">
          <div className="grid md:grid-cols-2 gap-16">
            <div>
              <h2 className="text-3xl font-semibold text-gray-900 mb-8">Project Overview</h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-8">
                Creative Sign Designs (CSD) is a national leader in architectural signage, known for its turnkey solutions and design-forward approach. I was brought in to evolve their brand while preserving its legacy—starting with a thoughtful update to the existing logo.
              </p>
              <p className="text-gray-600 leading-relaxed mb-8">
                The goal was to modernize the identity without losing recognition, and to establish a flexible visual system that could extend seamlessly across multiple divisions. I developed a modular brand pattern using the updated logo as a foundation—allowing each division to maintain a distinct identity while remaining cohesive under the CSD umbrella.
              </p>
              <p className="text-gray-600 leading-relaxed">
                Once the new brand direction was in place, I designed a clean, modern website that highlights CSD's impressive portfolio, trusted process, and national reach. The new site balances bold visuals with easy navigation, ensuring that prospective clients—whether architects, contractors, or property developers—can quickly understand the value CSD brings to every project.
              </p>
            </div>
            <div className="space-y-8">
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Client</h3>
                <p className="text-gray-600">Creative Sign Designs</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Timeline</h3>
                <p className="text-gray-600">6 weeks</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Services</h3>
                <p className="text-gray-600">Brand Identity, Website Design, Marketing Collateral</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Industry</h3>
                <p className="text-gray-600">Commercial Signage & Graphics</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Before & After Section */}
      <div className="py-24">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-3 gap-8 items-start">
            {/* Before - Left side, smaller */}
            <div className="col-span-1">
              <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Before</div>
              <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200">
                <OptimizedImage 
                  src="/lovable-uploads/e558d393-c7f6-4467-95df-4e8aa6026ed2.webp"
                  alt="Creative Sign Designs old logo"
                  className="w-full h-auto"
                />
              </div>
            </div>
            
            {/* After - Right side, larger */}
            <div className="col-span-2">
              <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">After</div>
              <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
                <OptimizedImage 
                  src="/lovable-uploads/74659be9-0652-4012-a20c-a2f1ecc2ece6.webp"
                  alt="Creative Sign Designs new brand system"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Logo Design Section */}
      <div className="py-24">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Brand Identity</div>
          <div className="space-y-6 text-gray-600 leading-relaxed">
            <p>
              Creative Sign Designs approached me with a logo that had strong brand recognition but needed a thoughtful update. The goal wasn't to replace it, but to evolve it—preserving its legacy while modernizing it for today's digital and print demands.
            </p>
            <p>
              I refined the existing "S" mark for cleaner geometry and better balance, while introducing a new, modern typeface to replace the compressed original. This brought clarity, legibility, and a more premium feel across all uses. By standardizing the typography, I created a more consistent brand experience—no matter where or how the logo appears.
            </p>
            <p>
              From there, I built a flexible identity system for CSD's three main divisions: EGD Studio, Architectural, and National Brands. Each uses the same core logo structure but with its own distinct color treatment, allowing each group to stand out while still feeling like part of the same family.
            </p>
            <p>
              This evolution brings cohesion, flexibility, and a polished visual system that reflects where CSD is headed—without losing sight of where it's been.
            </p>
          </div>
        </div>
      </div>

      {/* Website Before & After Section */}
      <div className="py-24">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-3 gap-8 items-start">
            {/* Before - Left side, smaller */}
            <div className="col-span-1">
              <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Before</div>
              <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200">
                <OptimizedImage 
                  src="/lovable-uploads/4eaaa1b2-3e4e-4947-aa8e-c0a72bb4261e.webp"
                  alt="Creative Sign Designs old website"
                  className="w-full h-auto"
                />
              </div>
            </div>
            
            {/* After - Right side, larger */}
            <div className="col-span-2">
              <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">After</div>
              <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
                <OptimizedImage 
                  src="/lovable-uploads/1aad1d9d-6884-4afc-bcdc-d60b61ab1580.webp"
                  alt="Creative Sign Designs new website"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Website Design Section */}
      <div className="py-24">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">THE WEBSITE</div>
          <div className="space-y-6 text-gray-600 leading-relaxed">
            <p>
              The goal of the website redesign for Creative Sign Designs was to elevate the brand's digital presence with a more modern, sophisticated look—one that speaks to national enterprise clients while still maintaining the local, friendly personality CSD is known for.
            </p>
            <p>
              The previous site felt crowded and dated, with dense blocks of text, inconsistent layout hierarchy, and limited breathing room. My redesign focused on clarity, space, and structure. I introduced a clean, modular layout that mirrors the updated logo system—bringing visual consistency across brand and interface.
            </p>
            <p>
              Typography was overhauled to be modern and readable, with a refined scale and hierarchy that communicates professionalism without feeling stiff. Strategic use of white space gives the content room to breathe, while hero visuals and bold statements help reinforce CSD's authority in the signage industry.
            </p>
            <p>
              The new site puts storytelling and user experience first—highlighting capabilities, showcasing past work, and making it easy for customers to understand CSD's value at a glance. The result is a site that feels elevated but accessible, corporate but human—just like CSD itself.
            </p>
          </div>
        </div>
      </div>

      {/* Website Screenshots */}
      <div className="py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid md:grid-cols-3 gap-8">
            <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200">
              <OptimizedImage 
                src="/lovable-uploads/97eee228-94d7-4c85-a448-9d27579f33d8.webp"
                alt="CSD Team page screenshot"
                className="w-full h-[500px] object-cover object-top"
              />
            </div>
            <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200">
              <OptimizedImage 
                src="/lovable-uploads/96d47567-3252-4373-94fb-a6ecb1d23152.webp"
                alt="CSD Portfolio page screenshot"
                className="w-full h-[500px] object-cover object-top"
              />
            </div>
            <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200">
              <OptimizedImage 
                src="/lovable-uploads/34abd571-a7ed-498d-a7a7-9a6b860d4a2e.webp"
                alt="CSD Project page screenshot"
                className="w-full h-[500px] object-cover object-top"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Brand Guidelines Section */}
      <div className="py-24">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Brand Guidelines</div>
          <div className="space-y-6 text-gray-600 leading-relaxed">
            <p>
              As part of the rebranding effort for Creative Sign Designs, I developed a comprehensive brand guidelines document to ensure clarity, cohesion, and consistency across all applications of the visual identity.
            </p>
            <p>
              The guidelines detail everything from logo usage and division-specific color palettes to updated typography, layout rules, and tone of voice. I introduced a bold but flexible visual language anchored by the refined logo and modular system—making it easy for internal teams and vendors to apply the brand correctly, whether they're designing a business card or a slide deck.
            </p>
            <p>
              The document includes real-world examples like stationery, presentation templates, and signage photography to show how the brand lives across mediums. This not only protects the integrity of the identity as CSD grows—it empowers the entire organization to communicate with confidence and visual clarity.
            </p>
          </div>
        </div>
      </div>

      {/* Brand Guidelines Section */}
      <div className="py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200">
            <OptimizedImage 
              src="/lovable-uploads/c3893efe-1e8a-484f-af86-a2009728aa54.webp"
              alt="Creative Sign Designs brand guidelines showing slides, stationery, and business cards"
              className="w-full h-auto"
            />
          </div>
        </div>
      </div>

      <ContactFooter />
    </div>
  );
};

export default CsdCaseStudy;
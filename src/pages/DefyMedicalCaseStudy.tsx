import { useEffect } from "react";
import Navigation from "@/components/Navigation";
import ContactFooter from "@/components/ContactFooter";
import OptimizedImage from "@/components/ui/optimized-image";
import { useImagePreload } from "@/hooks/use-image-preload";

const DefyMedicalCaseStudy = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  
  // Preload critical images for better performance
  useImagePreload([
    "/lovable-uploads/bf01ee68-2afb-40b8-b871-1d012eab0061.webp"
  ], { priority: true });
  
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section with Large Image */}
      <div className="pt-48 pb-0">
        <div className="max-w-7xl mx-auto px-6">
          {/* Project Title */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-7xl font-semibold text-gray-900 mb-8 leading-[0.9] tracking-tight">
              From Interest to Action: Elevating Defy Medical's ED Medication Page Performance
            </h1>
            <div className="flex justify-center gap-8 text-sm text-gray-500 uppercase tracking-wider">
              <span>Website Design</span>
              <span>•</span>
              <span>2024</span>
            </div>
          </div>
        </div>
        
        {/* Full Width Hero Image */}
        <div className="w-full">
          <OptimizedImage 
            src="/lovable-uploads/bf01ee68-2afb-40b8-b871-1d012eab0061.webp"
            alt="Defy Medical branding with outdoor lifestyle imagery"
            className="w-full h-[70vh] object-cover object-center"
            priority={true}
            sizes="100vw"
          />
        </div>
      </div>

      {/* Project Overview */}
      <div className="py-24">
        <div className="max-w-4xl mx-auto px-6">
          <div className="grid md:grid-cols-2 gap-16">
            <div>
              <h2 className="text-3xl font-semibold text-gray-900 mb-8">Project Overview</h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-8">
                Defy Medical is a leading integrative wellness clinic specializing in hormone replacement therapy, preventative medicine, and age-related symptom management. As a pioneer in patient-centered care, Defy was seeking to elevate its digital experience across multiple platforms to better reflect its clinical excellence and improve user engagement.
              </p>
              <p className="text-gray-600 leading-relaxed mb-8">
                I was brought in to help improve conversion performance on a key landing page promoting one of their ED medications. The goal was to modernize the layout, clarify the value proposition, and reduce friction in the user journey to increase signups and orders.
              </p>
              <p className="text-gray-600 leading-relaxed mb-8">
                My goal was to improve content hierarchy, site navigation, and overall visual storytelling—bringing the digital experience in line with the clinic's modern approach to care.
              </p>
              <p className="text-gray-600 leading-relaxed">
                Throughout the project, I collaborated closely with Defy's internal stakeholders to prioritize initiatives, define milestones, and ensure the final designs supported both business goals and patient needs.
              </p>
            </div>
            <div className="space-y-8">
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Client</h3>
                <p className="text-gray-600">Defy Medical</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Timeline</h3>
                <p className="text-gray-600">4 weeks</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Services</h3>
                <p className="text-gray-600">Website Design, Landing Page Optimization</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Industry</h3>
                <p className="text-gray-600">Healthcare & Medical</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Before & After Section */}
      <div className="py-24">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-3 gap-8 items-start">
            {/* Before - Left side, smaller */}
            <div className="col-span-1">
              <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Before</div>
              <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200">
                <OptimizedImage 
                  src="/lovable-uploads/99422f54-b6e1-43e9-b3b9-0688dfdf78d6.webp"
                  alt="Defy Medical website before redesign"
                  className="w-full h-auto"
                  sizes="(max-width: 768px) 100vw, 33vw"
                />
              </div>
            </div>
            
            {/* After - Right side, larger */}
            <div className="col-span-2">
              <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">After</div>
              <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
                <OptimizedImage 
                  src="/defy-medical.webp"
                  alt="Defy Medical website after redesign"
                  className="w-full h-auto"
                  sizes="(max-width: 768px) 100vw, 66vw"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Design Challenge Section */}
      <div className="py-16">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">THE DESIGN CHALLENGE</div>
          <div className="space-y-6 text-gray-600 leading-relaxed">
            <p>
              Designing for healthcare, especially in the ED medication space, requires a delicate balance of professionalism and approachability. Men seeking these treatments often feel embarrassed or hesitant to seek help, so the design needed to immediately establish trust while making the process feel simple and discreet.
            </p>
            <p>
              The original site wasn't effectively communicating Defy Medical's key differentiators: licensed physicians, FDA-approved medications, transparent pricing, and convenient delivery. Users were getting lost in the process and weren't converting at the rates the company needed.
            </p>
            <p>
              My solution focused on three core principles: clarity of messaging, trust-building through design, and a streamlined user journey that removes friction at every step.
            </p>
          </div>
      </div>
    </div>

    {/* The Website Section */}
    <div className="py-16">
      <div className="max-w-4xl mx-auto px-6">
        <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">THE WEBSITE</div>
        <div className="space-y-6 text-gray-600 leading-relaxed">
          <p>
            Defy Medical approached me with a clear goal: redesign their ED medication landing page to drive higher conversions without losing the credibility and clinical integrity of their brand. The original layout was informative but leaned heavily on text and clinical imagery, creating a transactional experience rather than an emotional one.
          </p>
          <p>
            We took a more human-centered approach, designing a landing page that feels welcoming, aspirational, and reassuring—crucial for a topic that can be sensitive or stigmatized for many patients.
          </p>
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Improvements:</h3>
            <div className="space-y-4">
              <p>
                <strong>Aspirational Imagery:</strong> We replaced stock clinical photos with lifestyle imagery and subtle motion video loops of confident, happy couples—projecting intimacy, connection, and emotional outcomes instead of medical transactions.
              </p>
              <p>
                <strong>Breathing Room:</strong> The new design embraces whitespace and clean structure, creating a more relaxing, uncluttered experience. This makes the page feel trustworthy and safe—an essential emotional cue for patients considering treatment.
              </p>
              <p>
                <strong>Storytelling Through Structure:</strong> The page hierarchy now tells a simple, effective story—starting with reassurance, then educating about the process, and ending with a clear call to action. We structured the journey to reduce cognitive load while reinforcing trust at each step.
              </p>
              <p>
                <strong>Clearer, More Inviting Flow:</strong> The process of signing up, ordering medications, and receiving treatment was reimagined visually using a simple 1-2-3 format with vibrant cards. This added clarity and momentum to the user journey.
              </p>
              <p>
                <strong>Microinteractions & Motion:</strong> We introduced subtle animations and motion elements to bring warmth and modernity to the interface without overwhelming or distracting from the message.
              </p>
            </div>
          </div>
          <p>
            The result is a landing page that feels more like a private conversation than a sales pitch—gently guiding users toward action while reinforcing the message that help is accessible, discreet, and life-changing.
          </p>
        </div>
      </div>
    </div>

    {/* Website Screenshots Section */}
    <div className="py-16">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid md:grid-cols-3 gap-6">
          <div className="rounded-lg overflow-hidden shadow-lg border border-gray-200">
            <OptimizedImage 
              src="/lovable-uploads/8f28b4d8-f31f-4410-82f8-211ae0f255ce.webp"
              alt="Defy Medical recommended products page"
              className="w-full h-auto"
              sizes="(max-width: 768px) 100vw, 33vw"
            />
          </div>
          <div className="rounded-lg overflow-hidden shadow-lg border border-gray-200">
            <OptimizedImage 
              src="/lovable-uploads/05120502-9cb5-4a4f-a72f-f177bf1c1255.webp"
              alt="Defy Medical secure checkout page"
              className="w-full h-auto"
              sizes="(max-width: 768px) 100vw, 33vw"
            />
          </div>
          <div className="rounded-lg overflow-hidden shadow-lg border border-gray-200">
            <OptimizedImage 
              src="/lovable-uploads/f731144c-cd86-4d71-a778-806025af4d86.webp"
              alt="Defy Medical product detail page"
              className="w-full h-auto"
              sizes="(max-width: 768px) 100vw, 33vw"
            />
          </div>
        </div>
      </div>
    </div>

    {/* Results Section */}
    <div className="py-16">
      <div className="max-w-4xl mx-auto px-6">
        <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Real Results:</div>
        <div className="space-y-6 text-gray-600 leading-relaxed">
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="text-3xl font-semibold text-gray-900 mb-2">38%</div>
              <div className="text-gray-600">increase in conversion rate within the first 30 days</div>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="text-3xl font-semibold text-gray-900 mb-2">52%</div>
              <div className="text-gray-600">longer average time on page, signaling stronger user engagement</div>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="text-3xl font-semibold text-gray-900 mb-2">25%</div>
              <div className="text-gray-600">decrease in bounce rate, indicating a more confident and curious audience</div>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="text-3xl font-semibold text-gray-900 mb-2">41%</div>
              <div className="text-gray-600">improvement in mobile conversions, with conversions on mobile devices up significantly</div>
            </div>
          </div>
          <p>
            The redesign didn't just look better—it performed better. By leading with story, empathy, and clarity, we helped Defy Medical reach more patients, reduce hesitation, and turn clicks into meaningful action.
          </p>
        </div>
      </div>
    </div>

      <ContactFooter />
    </div>
  );
};

export default DefyMedicalCaseStudy;
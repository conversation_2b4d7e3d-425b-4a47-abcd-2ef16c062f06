import Navigation from "@/components/Navigation";
import ContactFooter from "@/components/ContactFooter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Calendar, Users, BarChart3, Eye, Briefcase, Rocket, Laptop, Target as TargetIcon, RefreshCw, TrendingUp, Layers } from "lucide-react";

const FoundersCohort = () => {
  return (
    <div className="min-h-screen">
      <Navigation />
      
      {/* Hero Section */}
      <section className="min-h-screen flex items-center justify-center px-6 py-20 pt-32 relative">
        <div className="max-w-5xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <div className="mb-8">
              <span className="inline-block px-4 py-2 bg-white/80 backdrop-blur-sm border border-gray-200 text-gray-700 text-sm font-medium rounded-full mb-8 shadow-sm">
                Limited Availability
              </span>
            </div>
            
            <h1 className="text-6xl md:text-8xl font-semibold text-gray-900 mb-8 leading-[0.9] tracking-tight">
              Design a Company
              <br />
              <span className="bg-gradient-to-b from-[#474787] to-[#3d3f73] bg-clip-text text-transparent">That Looks Fundable</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-600 mb-12 font-light max-w-3xl mx-auto leading-relaxed">
              I help startup founders craft brands, pitch decks, and digital products that earn 
              investor trust and establish the foundation for sustainable growth.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
              <Button 
                size="lg" 
                className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-6 text-lg rounded-full transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl"
                asChild
              >
                <a href="/cohort-application">
                  Apply for Next Cohort
                  <ArrowRight className="w-5 h-5" />
                </a>
              </Button>
            </div>
            
            <div className="flex items-center justify-center gap-2 text-[#474787] font-medium">
              <Calendar className="w-4 h-4" />
              <span>Next cohort begins August 15 — Limited to 10 founders</span>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 text-center mt-8">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/40 shadow-sm">
              <div className="text-4xl font-black text-gray-900 mb-2">25+</div>
              <p className="text-gray-600">Years experience</p>
            </div>
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/40 shadow-sm">
              <div className="text-4xl font-black text-gray-900 mb-2">$1B+</div>
              <p className="text-gray-600">Capital raised</p>
            </div>
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/40 shadow-sm">
              <div className="text-4xl font-black text-gray-900 mb-2">7</div>
              <p className="text-gray-600">Clients Acquired</p>
            </div>
          </div>
          
          {/* Testimonials Section */}
          <div className="mt-20 pb-16 overflow-hidden">
            <div className="max-w-7xl mx-auto px-6">
              <div className="mb-16">
                <p className="text-sm text-gray-500 uppercase tracking-wider font-medium">
                  WHAT FOUNDERS ARE SAYING
                </p>
              </div>
              
              <div className="flex animate-testimonial-scroll gap-6 whitespace-nowrap">
                {/* Testimonial 1 - Nymbus */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/67d2aa26-b8d5-4b54-90e1-06dfb5fd76ed.png" 
                      alt="Nymbus" 
                      className="w-[165px]" 
                      onError={(e) => {
                        console.error('Nymbus logo failed to load:', e.currentTarget.src);
                        e.currentTarget.style.display = 'none';
                      }}
                      onLoad={() => console.log('Nymbus logo loaded successfully')}
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
                    "Mario helped us own the transformation story that became everything in our raises—we stopped defending the old way and started <span className="font-bold text-gray-900">selling the future</span>."
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden">
                      <img 
                        src="/lovable-uploads/e1ee491b-4aa5-4c8c-b9fe-844882dbaa4b.png" 
                        alt="Scott Killoh"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Scott Killoh</div>
                      <div className="text-gray-500">Founder</div>
                    </div>
                  </div>
                </div>

                {/* Gray divider */}
                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>

                {/* Testimonial 2 - Care.ai */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/f945ab41-2aca-47bd-b6b1-6c2a9b23a924.png"
                      alt="Care.ai"
                      className="w-[165px]"
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
"Mario understood what we were building before we could fully articulate it and helped us present it like the billion-dollar company that Stryker eventually <span className="font-bold text-gray-900">acquired</span>."
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden bg-gray-200">
                      <img 
                        src="/lovable-uploads/c72d7a75-855d-4ff5-aafb-266183e7d7ab.png"
                        alt="Chakri Toleti"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Chakri Toleti</div>
                      <div className="text-gray-500">Founder, Care.ai</div>
                    </div>
                  </div>
                </div>

                {/* Gray divider */}
                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>

                {/* Testimonial 3 - MamaBear */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/0de8cf66-428e-4686-91d4-ca0280267bc4.png" 
                      alt="MamaBear" 
                      className="w-[165px]"
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
"Mario didn't just 'design' our app, he understood the emotion behind it. He helped us <span className="font-bold text-gray-900">communicate trust</span> through every pixel."
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden bg-gray-200">
                      <img 
                        src="/lovable-uploads/2c6784ab-5dd9-4d16-84dd-7c1126a2ca2e.png" 
                        alt="Robyn Spoto" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Robyn Spoto</div>
                      <div className="text-gray-500">Founder, MamaBear App</div>
                    </div>
                  </div>
                </div>

                {/* Gray divider */}
                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>

                {/* Testimonial 4 - RealCar NYC */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/f8ec0e9a-7ba9-46eb-9658-92c33168966b.png" 
                      alt="RealCar NYC" 
                      className="w-[165px]"
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
"Mario <span className="font-bold text-gray-900">transformed</span> how investors saw our luxury rental platform—we went from 'another car service' to 'the premium experience NYC deserves.'"
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden bg-gray-200">
                      <img 
                        src="/lovable-uploads/2ac29322-4dfb-4323-bf29-e75b5294bfbc.png" 
                        alt="Gleb Tryapitsyn" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Gleb Tryapitsyn</div>
                      <div className="text-gray-500">Founder, RealCar NYC</div>
                    </div>
                  </div>
                </div>

                {/* Gray divider */}
                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>

                {/* Testimonial 5 - Finxact */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/5b65ee2b-1a3b-48e0-b09c-2097ac3e7508.png" 
                      alt="Finxact" 
                      className="w-[165px]"
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
                    "Most designers make things look good, but Mario makes <span className="font-bold text-gray-900">companies look fundable</span>. The rebrand he created was the foundation for our successful raise."
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden bg-gray-200">
                      <img 
                        src="/lovable-uploads/f403207d-dda0-455a-b982-821fe2f8380c.png" 
                        alt="Michael Sanchez" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Michael Sanchez</div>
                      <div className="text-gray-500">Founder, Finxact</div>
                    </div>
                  </div>
                </div>

                {/* Gray divider */}
                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>

                {/* Testimonial 6 - Bravo Care */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/a28f4836-9abb-49c0-b709-0b949757271a.png" 
                      alt="Bravo Care" 
                      className="w-[165px]"
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
"Mario gave us a new way to position ourselves in healthcare staffing that made investors go from confused about our approach to excited about our <span className="font-bold text-gray-900">competitive advantage</span>."
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden bg-gray-200">
                      <img 
                        src="/lovable-uploads/6af72459-419a-4c0c-bd95-75f17113a3cf.png" 
                        alt="Andrew Toobi" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Andrew Toobi</div>
                      <div className="text-gray-500">Founder, Bravo Care</div>
                    </div>
                  </div>
                </div>

                {/* Duplicate all testimonials for seamless loop */}
                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>
                
                {/* Duplicate 1 - Nymbus */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img src="/lovable-uploads/67d2aa26-b8d5-4b54-90e1-06dfb5fd76ed.png" alt="Nymbus" className="w-[165px]" />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
                    "Mario helped us own the transformation story that became everything in our raises—we stopped defending the old way and started <span className="font-bold text-gray-900">selling the future</span>."
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden">
                      <img 
                        src="/lovable-uploads/e1ee491b-4aa5-4c8c-b9fe-844882dbaa4b.png" 
                        alt="Scott Killoh"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Scott Killoh</div>
                      <div className="text-gray-500">Founder</div>
                    </div>
                  </div>
                </div>

                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>

                {/* Duplicate 3 - MamaBear */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/0de8cf66-428e-4686-91d4-ca0280267bc4.png" 
                      alt="MamaBear" 
                      className="w-[165px]"
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
                    "Mario didn't just 'design' our app, he understood the emotion behind it. He helped us <span className="font-bold text-gray-900">communicate trust</span> through every pixel."
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden bg-gray-200">
                      <img 
                        src="/lovable-uploads/2c6784ab-5dd9-4d16-84dd-7c1126a2ca2e.png" 
                        alt="Robyn Spoto" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Robyn Spoto</div>
                      <div className="text-gray-500">Founder, MamaBear App</div>
                    </div>
                  </div>
                </div>

                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>

                {/* Duplicate 4 - RealCar NYC */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/f8ec0e9a-7ba9-46eb-9658-92c33168966b.png" 
                      alt="RealCar NYC" 
                      className="w-[165px]"
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
                    "Mario <span className="font-bold text-gray-900">transformed</span> how investors saw our luxury rental platform—we went from 'another car service' to 'the premium experience NYC deserves.'"
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden bg-gray-200">
                      <img 
                        src="/lovable-uploads/2ac29322-4dfb-4323-bf29-e75b5294bfbc.png" 
                        alt="Gleb Tryapitsyn" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Gleb Tryapitsyn</div>
                      <div className="text-gray-500">Founder, RealCar NYC</div>
                    </div>
                  </div>
                </div>

                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>

                {/* Duplicate 5 - Finxact */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/5b65ee2b-1a3b-48e0-b09c-2097ac3e7508.png" 
                      alt="Finxact" 
                      className="w-[165px]"
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
                    "Most designers make things look good, but Mario makes <span className="font-bold text-gray-900">companies look fundable</span>. The rebrand he created was the foundation for our successful raise."
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden bg-gray-200">
                      <img 
                        src="/lovable-uploads/f403207d-dda0-455a-b982-821fe2f8380c.png" 
                        alt="Michael Sanchez" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Michael Sanchez</div>
                      <div className="text-gray-500">Founder, Finxact</div>
                    </div>
                  </div>
                </div>

                <div className="w-px bg-gray-300 flex-shrink-0 mx-6" style={{ height: '400px' }}></div>

                {/* Duplicate 6 - Bravo Care */}
                <div className="flex-shrink-0" style={{ width: '438px' }}>
                  <div className="mb-8 text-left h-20 flex items-start">
                    <img 
                      src="/lovable-uploads/a28f4836-9abb-49c0-b709-0b949757271a.png" 
                      alt="Bravo Care" 
                      className="w-[165px]"
                    />
                  </div>
                  <blockquote className="text-gray-700 text-3xl font-medium mb-12 leading-10 text-left whitespace-normal">
                    "Mario gave us a new way to position ourselves in healthcare staffing that made investors go from confused about our approach to excited about our <span className="font-bold text-gray-900">competitive advantage</span>."
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-full mr-4 overflow-hidden bg-gray-200">
                      <img 
                        src="/lovable-uploads/6af72459-419a-4c0c-bd95-75f17113a3cf.png" 
                        alt="Andrew Toobi" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">Andrew Toobi</div>
                      <div className="text-gray-500">Founder, Bravo Care</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </section>

      {/* Case Studies Section */}
      <section id="case-studies" className="py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-6">
              They didn't just launch — they stood out
            </h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {/* Nymbus Case Study */}
            <div 
              className="relative rounded-3xl overflow-hidden h-96 group cursor-pointer"
              style={{
                backgroundImage: `url('/lovable-uploads/b040416e-3d57-4fde-911d-c55ec2ae9502.png')`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
              <div className="absolute bottom-0 left-0 p-8 text-white">
                <h3 className="text-3xl font-bold mb-2">NYMBUS</h3>
                <p className="text-xl mb-4 font-medium">$329 Million Raised</p>
                <p className="text-lg mb-6 opacity-90">From fintech concept to Series C leader</p>
                <div className="flex gap-3 mb-6">
                  <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm">Fintech</span>
                  <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm">Banking</span>
                  <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm">Banking</span>
                </div>
                <Button 
                  variant="outline" 
                  className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
                  asChild
                >
                  <a href="/case-study/nymbus">
                    View Case Study
                  </a>
                </Button>
              </div>
            </div>

            {/* Care.ai Case Study */}
            <div 
              className="relative rounded-3xl overflow-hidden h-96 group cursor-pointer"
              style={{
                backgroundImage: `url('/lovable-uploads/0c3a835b-8176-461a-bbd7-e23b5ca7186d.png')`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
              <div className="absolute bottom-0 left-0 p-8 text-white">
                <h3 className="text-3xl font-bold mb-2">CARE.AI</h3>
                <p className="text-xl mb-4 font-medium">Acquired by Stryker</p>
                <p className="text-lg mb-6 opacity-90">From complex AI to strategic acquisition</p>
                <div className="flex gap-3 mb-6">
                  <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm">Healthcare</span>
                  <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm">AI</span>
                  <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm">Acquisition</span>
                </div>
                <Button 
                  variant="outline" 
                  className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
                >
                  View Case Study
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* 4-Week Program Section */}
      <section id="how-it-works" className="py-20 px-6 bg-white/30 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16 max-w-4xl mx-auto">
            <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              <span className="text-6xl md:text-7xl">4</span> weeks to transform how
              <br />
              investors <span className="text-gray-400">see your company</span>
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed">
              Join a small group of selected founders in a hands-on program designed to give you everything you need to look like a seasoned pro—even if this is your first startup.
            </p>
          </div>

          {/* Week Cards */}
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            {/* Week 1 - STORY */}
            <div className="group cursor-pointer">
              <div className="bg-gradient-to-br from-orange-400 to-red-500 rounded-3xl p-8 mb-6 transition-transform group-hover:scale-105">
                <div className="text-white text-sm font-medium mb-4">Week 1</div>
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Story</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Craft your narrative and positioning to resonate with investors and customers
              </p>
              <ul className="space-y-3 text-sm text-gray-600">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Value proposition</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Market positioning</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Competitor analysis</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Storytelling structure</span>
                </li>
              </ul>
            </div>

            {/* Week 2 - VISUALS */}
            <div className="group cursor-pointer">
              <div className="bg-gradient-to-br from-purple-400 to-purple-600 rounded-3xl p-8 mb-6 transition-transform group-hover:scale-105">
                <div className="text-white text-sm font-medium mb-4">Week 2</div>
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Visuals</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Develop a brand identity that communicates quality, trust, and scalability
              </p>
              <ul className="space-y-3 text-sm text-gray-600">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Brand identity</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Logo refinement</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Visual language</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Design principles</span>
                </li>
              </ul>
            </div>

            {/* Week 3 - ASSETS */}
            <div className="group cursor-pointer">
              <div className="bg-gradient-to-br from-blue-400 to-cyan-500 rounded-3xl p-8 mb-6 transition-transform group-hover:scale-105">
                <div className="text-white text-sm font-medium mb-4">Week 3</div>
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Assets</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Build the essential materials investors and partners need to take you seriously
              </p>
              <ul className="space-y-3 text-sm text-gray-600">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Pitch deck creation</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Website optimization</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Product mockups</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Financial models</span>
                </li>
              </ul>
            </div>

            {/* Week 4 - POLISH */}
            <div className="group cursor-pointer">
              <div className="bg-gradient-to-br from-green-400 to-emerald-500 rounded-3xl p-8 mb-6 transition-transform group-hover:scale-105">
                <div className="text-white text-sm font-medium mb-4">Week 4</div>
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Polish</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Refine every detail and prepare for flawless presentation to key stakeholders
              </p>
              <ul className="space-y-3 text-sm text-gray-600">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Pitch walkthrough</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Story refinement</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Follow-up assets</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Final tweaks</span>
                </li>
              </ul>
            </div>
          </div>

          {/* CTA */}
          <div className="text-center">
            <Button 
              size="lg" 
              className="bg-gray-900 hover:bg-gray-800 text-white px-12 py-6 text-lg rounded-full transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl mx-auto mb-4"
              asChild
            >
              <a href="/cohort-application">
                Apply Now
                <ArrowRight className="w-5 h-5" />
              </a>
            </Button>
            <p className="text-gray-600 italic">Limited to 10 founders per cohort</p>
          </div>
        </div>
      </section>

      {/* Who This Is For Section */}
      <section id="who-its-for" className="py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-6">
              Who This Helps
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              This intensive program is designed for serious founders who are ready to invest in their company's presentation and positioning.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Pre-Seed/Seed Founders */}
            <div className="bg-white/60 backdrop-blur-sm rounded-3xl p-8 border border-white/40 shadow-sm">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mb-6">
                <Rocket className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Pre-Seed & Seed Founders</h3>
              <p className="text-gray-600 leading-relaxed mb-4">
                You have a strong concept and early traction, but need to look the part when presenting to investors.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Building your first pitch deck
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Establishing brand credibility
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Preparing for investor meetings
                </li>
              </ul>
            </div>

            {/* Technical Founders */}
            <div className="bg-white/60 backdrop-blur-sm rounded-3xl p-8 border border-white/40 shadow-sm">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mb-6">
                <Laptop className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Technical Founders</h3>
              <p className="text-gray-600 leading-relaxed mb-4">
                You can build incredible products but struggle to communicate their value in business terms.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Translating tech to business value
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Visual storytelling skills
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Professional presentation materials
                </li>
              </ul>
            </div>

            {/* Experienced Entrepreneurs */}
            <div className="bg-white/60 backdrop-blur-sm rounded-3xl p-8 border border-white/40 shadow-sm">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mb-6">
                <TargetIcon className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Experienced Entrepreneurs</h3>
              <p className="text-gray-600 leading-relaxed mb-4">
                You've built companies before but want to elevate your presentation game for this venture.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Refining your narrative
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Modernizing brand presentation
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Standing out in competitive markets
                </li>
              </ul>
            </div>

            {/* Industry Pivots */}
            <div className="bg-white/60 backdrop-blur-sm rounded-3xl p-8 border border-white/40 shadow-sm">
              <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mb-6">
                <RefreshCw className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Industry Pivots</h3>
              <p className="text-gray-600 leading-relaxed mb-4">
                You're transitioning from another industry and need to establish credibility in tech.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Building tech credibility
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Professional repositioning
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Market positioning strategy
                </li>
              </ul>
            </div>

            {/* Scaling Startups */}
            <div className="bg-white/60 backdrop-blur-sm rounded-3xl p-8 border border-white/40 shadow-sm">
              <div className="w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center mb-6">
                <TrendingUp className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Scaling Startups</h3>
              <p className="text-gray-600 leading-relaxed mb-4">
                You have traction but need to communicate growth potential to Series A+ investors.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Growth story articulation
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Enterprise-grade materials
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Investor-ready presentations
                </li>
              </ul>
            </div>

            {/* B2B SaaS Founders */}
            <div className="bg-white/60 backdrop-blur-sm rounded-3xl p-8 border border-white/40 shadow-sm">
              <div className="w-16 h-16 bg-yellow-100 rounded-2xl flex items-center justify-center mb-6">
                <Layers className="w-8 h-8 text-yellow-600" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">B2B SaaS Founders</h3>
              <p className="text-gray-600 leading-relaxed mb-4">
                You're building enterprise software and need materials that communicate with decision-makers.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Enterprise sales materials
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  Complex solution simplification
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  ROI-focused messaging
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-6 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900"></div>
        <div className="max-w-5xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 bg-cyan-500 text-white text-sm font-medium rounded-full mb-8">
              Pricing & Application
            </div>
            <h2 className="text-5xl md:text-6xl font-semibold text-white mb-6">
              A premium investment
              <br />
              <span className="text-gray-400">in your company's future</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              The cohort program is a complete system to transform how investors see your 
              company. Transparent pricing, exceptional value.
            </p>
          </div>

          <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-3xl p-8 md:p-12">
            <div className="grid md:grid-cols-2 gap-12 items-start">
              {/* Program Details */}
              <div>
                <h3 className="text-3xl font-semibold text-white mb-6">Founder Cohort Program</h3>
                <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                  A 4-week intensive designed to make your startup look as 
                  fundable as it is. You'll get expert guidance, strategic design 
                  support, andhands-on creation of the assets that matter 
                  most — all tailored tohelp you stand out with investors and 
                  customers.
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-300">Limited to 10 founders per cohort for maximum focus</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-300">Weekly 1:1 strategy sessions tailored to your business</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-300">End-to-end creation of your brand identity, pitch deck, landing page, and 3custom UI/UX screens</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-300">Development of other key digital assets to support fundraising or go-to-</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-300">Ongoing support for 6 months as your company evolves</span>
                  </div>
                </div>
              </div>

              {/* Pricing */}
              <div className="text-center md:text-right">
                <div className="text-6xl font-bold text-white mb-2">$7,500</div>
                <div className="text-gray-400 text-lg mb-6">Per company</div>
                <div className="text-gray-300 mb-8">
                  Payment plans available
                  <br />
                  foraccepted founders
                </div>

                <Button 
                  size="lg" 
                  className="bg-white text-gray-900 hover:bg-gray-100 px-12 py-6 text-lg rounded-full transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl mx-auto md:mx-0 mb-6"
                  asChild
                >
                  <a href="/cohort-application">
                    Apply Now
                    <ArrowRight className="w-5 h-5" />
                  </a>
                </Button>

                <p className="text-gray-400 italic text-sm">No obligation assessment</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Mario Section */}
      <section id="about" className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-16">
            <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-6">
              I'm Mario Garcia, <span className="text-gray-500">and I've helped over</span>
              <br />
              <span className="text-gray-500">250 companies tell their story.</span>
            </h2>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-start">
            {/* Profile Image and Links */}
            <div className="text-center lg:text-left">
              <div className="relative inline-block mb-8">
                <div className="w-80 h-80 rounded-full overflow-hidden border-8 border-white shadow-2xl">
                  <img 
                    src="/lovable-uploads/93818653-fc86-4e39-b047-31e786976ad3.png" 
                    alt="Mario Garcia"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-white rounded-full border-4 border-gray-100 flex items-center justify-center shadow-lg">
                  <span className="text-2xl">👋</span>
                </div>
              </div>
              
              <div className="space-y-4">
                <a 
                  href="#" 
                  className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors font-medium"
                >
                  LinkedIn Profile
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
                <br />
                <a 
                  href="#" 
                  className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors font-medium"
                >
                  Twitter
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
            </div>

            {/* Bio Content */}
            <div className="space-y-6">
              <p className="text-xl text-gray-700 leading-relaxed">
                After 15 years leading design for both startups and Fortune 500 
                companies, I began to notice a pattern: even the most promising 
                ideas were struggling to gain traction — not because they lacked 
                merit, but because they looked unpolished and unconvincing.
              </p>

              <p className="text-xl text-gray-700 leading-relaxed">
                I've built this program based on what actually works—not theory, but 
                proven methods that have helped founders raise over $1 billion in capital 
                and achieve 9 successful exits.
              </p>

              <p className="text-xl text-gray-700 leading-relaxed">
                My background spans strategic design, product innovation, and investor 
                relations, with a proven track record of helping startups evolve their 
                products and position themselves for growth. I bring a founder-first 
                mindset to every engagement, delivering tangible outcomes that move 
                businesses forward.
              </p>

              <p className="text-xl text-gray-700 leading-relaxed">
                Beyond my work with founders, I advise leading venture capital firms on 
                how design can de-risk investments and enhance portfolio performance. I 
                also mentor at top accelerators, equipping the next generation of 
                entrepreneurs with the tools and insights needed to build fundable, high-impact ventures.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Custom Footer */}
      <footer className="bg-white py-24">
        <div className="max-w-6xl mx-auto px-6">
          {/* CTA Section */}
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-8 leading-tight">
              Ready to build a
              <br />
              fundable brand?
            </h2>
            
            <Button 
              className="bg-[#474787] hover:bg-[#3d3f73] text-white px-8 py-4 text-lg rounded-full font-semibold shadow-lg shadow-[#474787]/25 transition-all duration-200 hover:shadow-[#474787]/40 hover:scale-105 flex items-center gap-2 mx-auto mb-16 w-fit"
              asChild
            >
              <a href="/cohort-application">
                APPLY NOW
                <ArrowRight className="w-5 h-5" />
              </a>
            </Button>
          </div>

          {/* Navigation Links */}
          <div className="mb-16">
            <div className="flex flex-wrap justify-center gap-8 text-gray-600">
              <a href="#home" className="hover:text-gray-900 transition-colors">Home</a>
              <a href="#about" className="hover:text-gray-900 transition-colors">About</a>
              <a href="#works" className="hover:text-gray-900 transition-colors">Works</a>
              <a href="#contact" className="hover:text-gray-900 transition-colors">Contact</a>
            </div>
          </div>
          
          <div className="pt-8 border-t border-gray-200">
            <p className="text-gray-500 text-center">
              Copyright © 2025 Garcia Interactive. All Rights Reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default FoundersCohort;
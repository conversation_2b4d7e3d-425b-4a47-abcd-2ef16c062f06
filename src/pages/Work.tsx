import { Link } from "react-router-dom";
import Navigation from "@/components/Navigation";
import ContactFooter from "@/components/ContactFooter";

const projects = [
  {
    title: "Florida Funders | Rebrand and Website Design",
    slug: "floridafunders",
    image: "/lovable-uploads/2f26e8d5-cffc-4adb-b27e-7af5411c70c6.webp",
    tags: ["Web Design", "Brand Identity"],
    description: "Complete brand transformation and digital presence for Florida's leading investment firm."
  },
  {
    title: "New Brand Identity for GHWIN", 
    slug: "ghwin-case-study",
    image: "/lovable-uploads/04f55e1e-4ba1-4f78-99f0-8be0d0bd4c98.webp",
    tags: ["Brand Identity"],
    description: "Fresh brand identity design for a growing healthcare network."
  },
  {
    title: "Flare | AI Platform for Banks",
    slug: "flare-ai",
    image: "/lovable-uploads/0dbb6b31-8eba-4b4a-bc75-a00559542e2d.webp",
    tags: ["UI/UX", "Web Design", "Brand Identity"],
    description: "Enterprise AI platform design for financial institutions with focus on security and trust."
  },
  {
    title: "Defy Medical | Landing page redesign",
    slug: "defy-medical",
    image: "/lovable-uploads/ca9b1e05-c04a-43e9-a477-5a235072ed68.webp",
    tags: ["Website Design"],
    description: "User-friendly medical platform design for affordable ED medications."
  },
  {
    title: "Logo Update and Website Redesign for CSD",
    slug: "csd-redesign",
    image: "/lovable-uploads/********-ff6a-4b68-a722-c65f2c70cce3.webp",
    tags: ["Brand Identity", "Web Design"],
    description: "Complete visual identity refresh and digital platform development."
  }
];

const Work = () => {
  return (
    <div className="min-h-screen">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 px-6 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-7xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 bg-orange-500/20 backdrop-blur-md border border-orange-500/30 text-orange-600 px-4 py-2 rounded-full text-sm font-medium mb-8">
            <span className="text-orange-500 animate-pulse">✦</span>
            Portfolio
          </div>
          <h1 className="text-5xl md:text-7xl font-semibold text-gray-900 mb-6 leading-tight">
            Featured Work
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            A collection of brand identities, web designs, and digital experiences 
            crafted for ambitious companies and startups.
          </p>
        </div>
      </section>
      
      {/* Projects Grid */}
      <section className="py-16 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project, index) => (
              <Link 
                key={index} 
                to={`/case-study/${project.slug}`} 
                className="group block"
              >
                <div className="relative overflow-hidden rounded-2xl bg-gray-900 h-[400px] hover:transform hover:scale-[1.02] transition-all duration-500 hover:shadow-2xl">
                  {/* Project Image */}
                  <div className="absolute inset-0 overflow-hidden rounded-2xl">
                    <img 
                      src={project.image} 
                      alt={project.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-black/30 group-hover:bg-black/50 transition-colors duration-300"></div>
                  </div>
                  
                  {/* Content Overlay */}
                  <div className="absolute inset-0 p-6 flex flex-col justify-between">
                    {/* Tags */}
                    <div className="flex flex-wrap gap-2">
                      {project.tags.map((tag, tagIndex) => (
                        <span 
                          key={tagIndex}
                          className="px-3 py-1 bg-black/50 backdrop-blur-sm text-white text-sm rounded-full border border-white/20"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                    
                    {/* Title and Description */}
                    <div className="space-y-3">
                      <h3 className="text-xl font-bold text-white leading-tight group-hover:transform group-hover:translate-y-[-4px] transition-transform duration-300">
                        {project.title}
                      </h3>
                      <p className="text-gray-200 text-sm opacity-90 group-hover:opacity-100 transition-opacity duration-300">
                        {project.description}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
      
      <ContactFooter />
    </div>
  );
};

export default Work;
import { Hono } from "hono";
import { cors } from "hono/cors";
import { sendEmail } from "./postmark";

// Define the environment interface for Cloudflare Workers
interface Env {
  POSTMARK_SERVER_TOKEN: string;
  ALLOWED_ORIGINS: string;
}

// Create Hono app with environment type
const app = new Hono<{ Bindings: Env }>();

// Enable CORS for all origins
app.use(
  "/*",
  cors({
    origin: "*", // Allow all origins
    allowMethods: ["POST", "OPTIONS", "GET", "PUT", "DELETE", "PATCH"],
    allowHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
    exposeHeaders: ["Content-Length", "X-Requested-With"],
    credentials: false, // Changed to false to work with wildcard origin
    maxAge: 86400,
  })
);

// Health check endpoint
app.get("/", (c) => {
  return c.json({ status: "ok", message: "Email API is running" });
});

// Email sending endpoint
app.post("/api/send-email", async (c) => {
  try {
    const body = await c.req.json();

    // Validate required fields
    if (
      !body.to ||
      !body.subject ||
      (!body.htmlBody || !body.textBody)
    ) {
      return c.json({ error: "Missing required fields" }, 400);
    }

    // Send email using Postmark
    const result = await sendEmail(
      {
        from: "<EMAIL>",
        to: body.to,
        subject: body.subject,
        htmlBody: body.htmlBody,
        textBody: body.textBody,
        replyTo: body.replyTo,
        cc: body.cc,
        bcc: body.bcc,
        tag: body.tag,
        metadata: body.metadata,
        attachments: body.attachments,
      },
      c.env.POSTMARK_SERVER_TOKEN
    );

    return c.json({ success: true, result });
  } catch (error) {
    console.error("Error sending email:", error);
    return c.json(
      {
        error: "Failed to send email",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      500
    );
  }
});

export default app;

name = "mario-portfolio-api"
main = "src/worker/index.ts"
compatibility_date = "2023-10-02"

[vars]
POSTMARK_SERVER_TOKEN = "************************************"
ALLOWED_ORIGINS = "http://localhost:5173,https://your-production-domain.com"

# Customize routes if needed
# [routes]
# pattern = "/api/*"
# zone_name = "your-domain.com"

# Add your domain binding if needed
# [env.production]
# routes = [
#   { pattern = "api.your-domain.com", custom_domain = true }
# ]

[observability.logs]
enabled = true
head_sampling_rate = 0.4

